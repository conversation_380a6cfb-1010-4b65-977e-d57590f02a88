import { IProductListDTO, IProductListRawDTO } from "../dtos/product/get-all-products.dto";

/**
 * Normaliza os dados de produto vindos da API, convertendo category (string) para categories (array)
 * @param rawProduct - Produto bruto da API
 * @returns Produto normalizado com categories como array
 */
export const normalizeProductData = (rawProduct: IProductListRawDTO): IProductListDTO => {
	// Se já tem categories como array, usa ele
	if (rawProduct.categories && Array.isArray(rawProduct.categories)) {
		return {
			...rawProduct,
			categories: rawProduct.categories,
		};
	}

	// Se tem category como string, converte para array
	if (rawProduct.category && typeof rawProduct.category === "string") {
		return {
			...rawProduct,
			categories: [rawProduct.category],
		};
	}

	// Se não tem nenhum dos dois, retorna array vazio
	return {
		...rawProduct,
		categories: [],
	};
};

/**
 * Normaliza uma lista de produtos vindos da API
 * @param rawProducts - Lista de produtos brutos da API
 * @returns Lista de produtos normalizados
 */
export const normalizeProductListData = (rawProducts: IProductListRawDTO[]): IProductListDTO[] => {
	return rawProducts.map(normalizeProductData);
};
