export interface IProductListDTO {
	id: number;
	name: string;
	quantity: number;
	price: number;
	status: boolean;
	barcode: string;
	categories: string[];
}

// Interface para os dados brutos da API que podem vir com category ou categories
export interface IProductListRawDTO {
	id: number;
	name: string;
	quantity: number;
	price: number;
	status: boolean;
	barcode: string;
	category?: string;
	categories?: string[];
}

export interface IProductList {
	data: IProductListDTO[];
	total: number;
}

// Interface para os dados brutos da lista da API
export interface IProductListRaw {
	data: IProductListRawDTO[];
	total: number;
}

export interface IProductListProps {
	page: number;
	limit: number;
	filter?: string;
}
