import { queryOptions, useQuery } from "@tanstack/react-query";
import { useMemo } from "react";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { productsFindAllRequest } from "../../api/requests/product/find-all";
import { IProductList, IProductListRaw } from "../../dtos/product/get-all-products.dto";
import { normalizeProductListData } from "../../utils/normalize-product-data.util";

export const createFindAllProductQuery = ({ page, limit, filter }: { page: number; limit: number; filter?: string }) =>
	queryOptions({
		queryKey: ["products-find-all", { page, limit, filter }],
		queryFn: () => productsFindAllRequest({ page, limit, filter }),
		refetchOnWindowFocus: true,
	});

export const useFindAllProduct = ({ page = 1, limit = 10, filter }: { page?: number; limit?: number; filter?: string }) => {
	const queryOptions = createFindAllProductQuery({ page, limit, filter });
	const { data: rawData, isLoading, isError } = useQuery(queryOptions);

	// Normaliza os dados para garantir que categories seja sempre um array
	const data = useMemo((): ApiResponse<IProductList> | undefined => {
		if (!rawData) return undefined;

		if (!rawData.success) return rawData as ApiResponse<IProductList>;

		return {
			...rawData,
			data: {
				...rawData.data,
				data: normalizeProductListData(rawData.data.data),
			},
		};
	}, [rawData]);

	return { data, isLoading, isError };
};
