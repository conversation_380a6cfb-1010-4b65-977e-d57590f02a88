import { createRequest } from "@/shared/lib/create-request.lib";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { IProductListProps, IProductListRaw } from "../../../dtos/product/get-all-products.dto";
import { PRODUCT_ROUTES } from "../../endpoints";

export const productsFindAllRequest = async ({ ...items }: IProductListProps): Promise<ApiResponse<IProductListRaw>> => {
	const response = await createRequest<IProductListRaw>({
		path: `${PRODUCT_ROUTES.FIND_ALL({ page: items.page, limit: items.limit, filter: items.filter || "" })}`,
		method: "GET",
	});

	return response;
};
